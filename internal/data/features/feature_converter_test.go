package features

import (
	"strings"
	"testing"
)

// TestValidateTypeCompatibility tests the type compatibility validation.
func TestValidateTypeCompatibility(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		featureType string
		targetType  string
		wantErr     bool
		errMsg      string
	}{
		// Valid combinations
		{"nominal with string", "nominal", "string", false, ""},
		{"numeric with float", "numeric", "float", false, ""},
		{"numeric with integer", "numeric", "integer", false, ""},
		{"date with integer", "date", "integer", false, ""},
		{"datetime with integer", "datetime", "integer", false, ""},
		{"time with integer", "time", "integer", false, ""},
		{"binary with string", "binary", "string", false, ""},
		{"binary with integer", "binary", "integer", false, ""},
		
		// Case insensitive
		{"NOMINAL with STRING", "NOMINAL", "STRING", false, ""},
		
		// Invalid combinations
		{"nominal with float", "nominal", "float", true, "not compatible"},
		{"nominal with integer", "nominal", "integer", true, "not compatible"},
		{"numeric with string", "numeric", "string", true, "not compatible"},
		{"date with string", "date", "string", true, "not compatible"},
		{"binary with float", "binary", "float", true, "not compatible"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := converter.ValidateTypeCompatibility(tt.featureType, tt.targetType)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// TestGetDefaultTargetType tests the default target type mapping.
func TestGetDefaultTargetType(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name        string
		featureType string
		expected    string
		wantErr     bool
	}{
		{"nominal", "nominal", "string", false},
		{"numeric", "numeric", "float", false},
		{"date", "date", "integer", false},
		{"datetime", "datetime", "integer", false},
		{"time", "time", "integer", false},
		{"binary", "binary", "string", false},
		{"case insensitive", "NOMINAL", "string", false},
		{"invalid type", "invalid", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.GetDefaultTargetType(tt.featureType)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if result != tt.expected {
					t.Errorf("expected %q, got %q", tt.expected, result)
				}
			}
		})
	}
}

// TestConvertToFeatureType tests the conversion to internal FeatureType.
func TestConvertToFeatureType(t *testing.T) {
	converter := NewTypeConverter()

	tests := []struct {
		name       string
		targetType string
		expected   FeatureType
		wantErr    bool
	}{
		{"integer", "integer", IntegerFeature, false},
		{"float", "float", FloatFeature, false},
		{"string", "string", StringFeature, false},
		{"case insensitive", "INTEGER", IntegerFeature, false},
		{"whitespace", "  float  ", FloatFeature, false},
		{"invalid", "invalid", StringFeature, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertToFeatureType(tt.targetType)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if result != tt.expected {
					t.Errorf("expected %v, got %v", tt.expected, result)
				}
			}
		})
	}
}
