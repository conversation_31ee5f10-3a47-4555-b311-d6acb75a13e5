// Package features provides feature type conversion and compatibility validation.
//
// <PERSON>les type conversion at loading time, converting feature values into
// float, int, or string based on feature type and compatibility rules.
// Implements the conversion logic separated from YAML loading.
//
// Security: Validates type compatibility to prevent data processing errors
// Performance: Fast O(1) compatibility checks using lookup maps
package features

import (
	"fmt"
	"strings"
)

// TypeConverter handles feature type conversion and compatibility validation.
//
// Security: Validates type compatibility to prevent processing errors
// Performance: Stateless design enables concurrent usage
// Relationships: Used by data loading pipeline after YAML parsing
type TypeConverter struct{}

// NewTypeConverter creates a new type converter instance.
//
// Args: None
// Returns: converter ready for type conversion operations
func NewTypeConverter() *TypeConverter {
	return &TypeConverter{}
}

// ValidateTypeCompatibility validates logical compatibility between feature type and target conversion.
//
// Args:
// - featureType: Feature type from YAML (nominal, numeric, date, datetime, time, binary)
// - targetType: Target conversion type (string, integer, float)
//
// Returns: error for incompatible combinations, nil if compatible
// Performance: O(1) lookup using pre-computed compatibility map
// Constraints: Enforces strict type compatibility rules
//
// Compatibility rules:
// - nominal: string only
// - numeric: float, integer only
// - date/datetime/time: integer only
// - binary: string, integer only
func (tc *TypeConverter) ValidateTypeCompatibility(featureType, targetType string) error {
	normalizedType := strings.ToLower(strings.TrimSpace(featureType))
	normalizedTarget := strings.ToLower(strings.TrimSpace(targetType))

	// Fast O(1) lookup map for valid combinations
	validCombinations := map[string]bool{
		// nominal - string only
		"nominal:string": true,

		// numeric - float, integer only
		"numeric:float":   true,
		"numeric:integer": true,

		// date/datetime/time - integer only
		"date:integer":     true,
		"datetime:integer": true,
		"time:integer":     true,

		// binary - string, integer only
		"binary:string":  true,
		"binary:integer": true,
	}

	// Check if combination is valid
	key := normalizedType + ":" + normalizedTarget
	if validCombinations[key] {
		return nil
	}

	// Return specific error for invalid combination
	return fmt.Errorf("feature type '%s' is not compatible with target type '%s'",
		featureType, targetType)
}

// ConvertToFeatureType converts target type string to internal FeatureType.
//
// Args:
// - targetType: Target type string (integer, float, string)
//
// Returns:
// - FeatureType: corresponding internal type
// - error: if target type is invalid
//
// Relationships: Bridges conversion targets to internal type system
// Side effects: Determines internal data processing approach
func (tc *TypeConverter) ConvertToFeatureType(targetType string) (FeatureType, error) {
	switch strings.ToLower(strings.TrimSpace(targetType)) {
	case "integer":
		return IntegerFeature, nil
	case "float":
		return FloatFeature, nil
	case "string":
		return StringFeature, nil
	default:
		return StringFeature, fmt.Errorf("unsupported target type '%s'", targetType)
	}
}

// GetDefaultTargetType returns the default target type for a given feature type.
//
// Args:
// - featureType: Feature type from YAML (nominal, numeric, date, datetime, time, binary)
//
// Returns:
// - string: default target type for the feature type
// - error: if feature type is unsupported
//
// Default mappings:
// - nominal: string
// - numeric: float
// - date/datetime/time: integer
// - binary: string
func (tc *TypeConverter) GetDefaultTargetType(featureType string) (string, error) {
	normalizedType := strings.ToLower(strings.TrimSpace(featureType))

	switch normalizedType {
	case "nominal":
		return "string", nil
	case "numeric":
		return "float", nil
	case "date", "datetime", "time":
		return "integer", nil
	case "binary":
		return "string", nil
	default:
		return "", fmt.Errorf("unsupported feature type '%s'", featureType)
	}
}
