package features

import (
	"strings"
	"testing"
)

// TestValidateTypeCompatibility tests the type compatibility validation function.
func TestValidateTypeCompatibility(t *testing.T) {
	loader := NewFeatureLoader()

	tests := []struct {
		name        string
		featureType string
		handleAs    string
		wantErr     bool
		errMsg      string
	}{
		// Valid combinations
		{
			name:        "nominal with string - valid",
			featureType: "nominal",
			handleAs:    "string",
			wantErr:     false,
		},
		{
			name:        "numeric with float - valid",
			featureType: "numeric",
			handleAs:    "float",
			wantErr:     false,
		},
		{
			name:        "numeric with integer - valid",
			featureType: "numeric",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "date with integer - valid",
			featureType: "date",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "datetime with integer - valid",
			featureType: "datetime",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "time with integer - valid",
			featureType: "time",
			handleAs:    "integer",
			wantErr:     false,
		},
		{
			name:        "binary with string - valid",
			featureType: "binary",
			handleAs:    "string",
			wantErr:     false,
		},
		{
			name:        "binary with integer - valid",
			featureType: "binary",
			handleAs:    "integer",
			wantErr:     false,
		},
		// Case insensitive tests
		{
			name:        "NOMINAL with STRING - case insensitive",
			featureType: "NOMINAL",
			handleAs:    "STRING",
			wantErr:     false,
		},
		{
			name:        "Numeric with Float - mixed case",
			featureType: "Numeric",
			handleAs:    "Float",
			wantErr:     false,
		},
		// Invalid combinations
		{
			name:        "nominal with float - invalid",
			featureType: "nominal",
			handleAs:    "float",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		{
			name:        "nominal with integer - invalid",
			featureType: "nominal",
			handleAs:    "integer",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		{
			name:        "numeric with string - invalid",
			featureType: "numeric",
			handleAs:    "string",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		{
			name:        "date with string - invalid",
			featureType: "date",
			handleAs:    "string",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		{
			name:        "date with float - invalid",
			featureType: "date",
			handleAs:    "float",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		{
			name:        "binary with float - invalid",
			featureType: "binary",
			handleAs:    "float",
			wantErr:     true,
			errMsg:      "not compatible",
		},
		// Whitespace handling
		{
			name:        "whitespace handling - valid",
			featureType: "  nominal  ",
			handleAs:    "  string  ",
			wantErr:     false,
		},
		{
			name:        "whitespace handling - invalid",
			featureType: "  nominal  ",
			handleAs:    "  float  ",
			wantErr:     true,
			errMsg:      "not compatible",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := loader.validateTypeCompatibility(tt.featureType, tt.handleAs)

			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("expected error containing %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// BenchmarkValidateTypeCompatibility benchmarks the type compatibility validation.
func BenchmarkValidateTypeCompatibility(b *testing.B) {
	loader := NewFeatureLoader()
	
	// Test with a mix of valid and invalid combinations
	testCases := []struct {
		featureType string
		handleAs    string
	}{
		{"nominal", "string"},
		{"numeric", "float"},
		{"binary", "integer"},
		{"nominal", "float"}, // invalid
		{"date", "integer"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		testCase := testCases[i%len(testCases)]
		_ = loader.validateTypeCompatibility(testCase.featureType, testCase.handleAs)
	}
}
