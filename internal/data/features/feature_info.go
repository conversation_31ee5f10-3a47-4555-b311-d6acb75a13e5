// Package features provides feature type information and metadata management.
//
// Handles feature type definitions, conversions, and metadata storage for
// decision tree operations. Saves feature name, type, and distribution data
// from training datasets according to <PERSON><PERSON>berri specifications.
//
// Security: Thread-safe for concurrent access during training and prediction.
// Performance: Efficient metadata lookups and type conversions.
package features

// FeatureType represents the core feature types supported by Mulberri.
//
// Args: None (enum type)
// Constraints: Must be one of IntegerFeature, FloatFeature, StringFeature
// Security: No sensitive data handling
// Relationships: Maps to handle_as values from YAML configuration
// Side effects: Determines internal data processing and storage types
type FeatureType int

const (
	IntegerFeature FeatureType = iota // Converted to int64 internally
	FloatFeature                      // Converted to float64 internally
	StringFeature                     // Stored as string internally
)

// String returns the string representation of FeatureType.
//
// Args: None (method receiver)
// Returns: string representation of feature type
// Security: No sensitive data exposure
func (ft FeatureType) String() string {
	switch ft {
	case IntegerFeature:
		return "integer"
	case FloatFeature:
		return "float"
	case StringFeature:
		return "string"
	default:
		return "unknown"
	}
}

// FeatureInfo holds metadata about a single feature from training data.
//
// Security: Contains training data distribution (no PII directly)
// Performance: O(1) lookups for metadata, O(n) for distribution operations
// Relationships: Created during data loading, used throughout training
// Side effects: Distribution updates affect memory usage linearly
type FeatureInfo struct {
	Name         string              // Feature name (CSV column header)
	Type         FeatureType         // Internal processing type
	OriginalType string              // Original YAML type specification
	Distribution map[interface{}]int // Value distribution from training data
}

// NewFeatureInfo creates a new FeatureInfo with the specified parameters.
//
// Args:
// - name: Feature name (must be non-empty)
// - featureType: Internal processing type
// - originalType: Original YAML type specification
//
// Returns: initialized FeatureInfo with empty distribution
// Security: No validation of name content (assumes pre-validated)
// Relationships: Used by data loading process
func NewFeatureInfo(name string, featureType FeatureType, originalType string) *FeatureInfo {
	return &FeatureInfo{
		Name:         name,
		Type:         featureType,
		OriginalType: originalType,
		Distribution: make(map[interface{}]int),
	}
}

// IsNumerical returns true if the feature type is numeric (int or float).
//
// Args: None (method receiver)
// Returns: true for IntegerFeature or FloatFeature, false otherwise
// Relationships: Used by splitting algorithms to determine strategy
func (fi *FeatureInfo) IsNumerical() bool {
	return fi.Type == IntegerFeature || fi.Type == FloatFeature
}

// IsCategorical returns true if the feature type is categorical (string).
//
// Args: None (method receiver)
// Returns: true for StringFeature, false otherwise
// Relationships: Used for categorical splitting and missing value handling
func (fi *FeatureInfo) IsCategorical() bool {
	return fi.Type == StringFeature
}

// AddValue adds a value to the feature's distribution.
//
// Args:
// - value: Feature value to add (any comparable type)
//
// Side effects: Updates distribution map, increases memory usage
// Performance: O(1) hash map operation
func (fi *FeatureInfo) AddValue(value interface{}) {
	fi.Distribution[value]++
}

// GetUniqueValueCount returns the number of unique values seen for this feature.
//
// Args: None (method receiver)
// Returns: count of unique values in distribution
// Performance: O(1) operation using map length
func (fi *FeatureInfo) GetUniqueValueCount() int {
	return len(fi.Distribution)
}

// GetMostCommonValue returns the most frequently occurring value.
//
// Args: None (method receiver)
// Returns:
// - interface{}: most common value (nil if no values recorded)
// - int: count of occurrences (0 if no values recorded)
//
// Performance: O(n) iteration through distribution map
// Relationships: Used for missing value imputation
func (fi *FeatureInfo) GetMostCommonValue() (interface{}, int) {
	var mostCommon interface{}
	maxCount := 0

	for value, count := range fi.Distribution {
		if count > maxCount {
			maxCount = count
			mostCommon = value
		}
	}

	return mostCommon, maxCount
}

// FeatureInfoMap holds metadata for all features in a dataset.
//
// Security: Immutable after training data loading for consistency
// Performance: O(1) feature metadata lookups by name
// Relationships: Used throughout training and prediction workflows
type FeatureInfoMap map[string]*FeatureInfo

// NewFeatureInfoMap creates an empty feature info map.
//
// Args: None
// Returns: initialized empty map ready for population
// Side effects: Allocates memory for map structure
func NewFeatureInfoMap() FeatureInfoMap {
	return make(FeatureInfoMap)
}

// AddFeature adds a feature to the map.
//
// Args:
// - feature: FeatureInfo pointer to add (must be non-nil)
//
// Side effects: Stores feature in map, may overwrite existing feature
// Security: No validation of feature content (assumes pre-validated)
func (fim FeatureInfoMap) AddFeature(feature *FeatureInfo) {
	fim[feature.Name] = feature
}

// GetFeature retrieves feature metadata by name.
//
// Args:
// - name: Feature name to lookup
//
// Returns:
// - *FeatureInfo: feature metadata (nil if not found)
// - bool: true if found, false otherwise
//
// Performance: O(1) hash map lookup
func (fim FeatureInfoMap) GetFeature(name string) (*FeatureInfo, bool) {
	feature, exists := fim[name]
	return feature, exists
}

// GetFeatureNames returns all feature names in the map.
//
// Args: None (method receiver)
// Returns: slice of feature names
// Performance: O(n) where n is number of features
func (fim FeatureInfoMap) GetFeatureNames() []string {
	names := make([]string, 0, len(fim))
	for name := range fim {
		names = append(names, name)
	}
	return names
}
