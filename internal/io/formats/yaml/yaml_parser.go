// Package yaml provides YAML file parsing and validation utilities.
//
// Handles YAML format operations including file validation, content parsing,
// and structure validation. Used by feature metadata loading and other
// configuration file processing throughout Mulberri.
//
// Security: Validates file paths and content structure
// Performance: Efficient YAML operations with minimal memory allocations
package yaml

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// File size limits for YAML processing
const (
	// MaxYAMLFileSize limits YAML files to prevent memory exhaustion
	// Args: Size in bytes (1MB default)
	MaxYAMLFileSize = 1024 * 1024
)

// Validator provides YAML file validation capabilities.
//
// Security: Validates file paths and prevents malicious file access
// Performance: Stateless design enables concurrent usage
// Relationships: Used by feature loading and configuration management
type Validator struct{}

// ValidationError represents errors during YAML operations.
//
// Args: Structured error context for debugging and user feedback
// Security: May contain file paths (ensure no sensitive path exposure)
// Relationships: Used throughout YAML processing pipeline
type ValidationError struct {
	Op   string // Operation that failed
	File string // File path being processed
	Err  error  // Underlying error
}

// Error implements the error interface with structured context.
//
// Args: None (method receiver)
// Returns: formatted error message with operation context
func (e *ValidationError) Error() string {
	return fmt.Sprintf("YAML %s error in file '%s': %v", e.Op, e.File, e.Err)
}

// Unwrap returns the underlying error for error chain inspection.
//
// Args: None (method receiver)
// Returns: underlying error for error wrapping support
func (e *ValidationError) Unwrap() error {
	return e.Err
}

// NewValidator creates a new YAML validator instance.
//
// Args: None
// Returns: validator ready for file validation operations
// Security: Stateless design prevents state corruption
func NewValidator() *Validator {
	return &Validator{}
}

// ValidateFile validates that a file path points to a valid YAML file.
//
// Args:
// - filePath: Path to YAML file (relative or absolute)
//
// Returns:
// - string: cleaned and validated file path
// - error: validation error with specific failure reason
//
// Security: Prevents path traversal and validates file accessibility
// Performance: Early validation prevents expensive processing of invalid files
// Side effects: Checks file system for existence and permissions
//
// Validation includes:
// - Path security (no traversal attacks)
// - File existence and accessibility
// - File type validation (regular file)
// - Extension validation (.yaml or .yml)
// - Size limits (prevents large file abuse)
// - Read permission verification
func (v *Validator) ValidateFile(filePath string) (string, error) {
	// Basic path validation
	cleanPath := strings.TrimSpace(filePath)

	// Security validation
	if err := v.validateSecurePath(cleanPath); err != nil {
		return "", &ValidationError{
			Op:   "validate_security",
			File: filePath,
			Err:  fmt.Errorf("security validation failed: %w", err),
		}
	}

	// Clean and normalize path
	cleanPath = filepath.Clean(cleanPath)

	// Check file existence and properties
	if err := v.validateFileProperties(cleanPath); err != nil {
		return "", &ValidationError{
			Op:   "validate_properties",
			File: cleanPath,
			Err:  err,
		}
	}

	return cleanPath, nil
}

// validateSecurePath performs security validation to prevent path traversal attacks.
//
// Args:
// - filePath: File path to validate
//
// Returns: error if path appears malicious, nil if secure
// Security: Prevents directory traversal and other path-based attacks
func (v *Validator) validateSecurePath(filePath string) error {
	// Check for path traversal attempts
	if strings.Contains(filePath, "..") {
		return fmt.Errorf("path traversal detected: %s", filePath)
	}

	// Check for null bytes (can cause issues in some systems)
	if strings.Contains(filePath, "\x00") {
		return fmt.Errorf("null byte detected in path: %s", filePath)
	}

	// Check for suspicious patterns
	suspiciousPatterns := []string{
		"//",    // Double slashes
		"\\.\\", // Windows path traversal
	}

	for _, pattern := range suspiciousPatterns {
		if strings.Contains(filePath, pattern) {
			return fmt.Errorf("suspicious path pattern detected: %s", filePath)
		}
	}

	return nil
}



// validateFileProperties validates file existence, type, size, and permissions.
//
// Args:
// - filePath: File path to validate properties
//
// Returns: error if any property validation fails, nil if all valid
// Side effects: Performs file system operations for validation
func (v *Validator) validateFileProperties(filePath string) error {
	// Check file existence and get info
	info, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", filePath)
	}
	if err != nil {
		return fmt.Errorf("cannot access file: %w", err)
	}

	// Verify it's a regular file
	if !info.Mode().IsRegular() {
		return fmt.Errorf("path is not a regular file: %s", filePath)
	}

	// Check file size limits
	if info.Size() > MaxYAMLFileSize {
		return fmt.Errorf("file too large: %d bytes (max %d bytes)",
			info.Size(), MaxYAMLFileSize)
	}

	// Verify read permissions by attempting to open
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("cannot read file: %w", err)
	}
	defer file.Close()

	return nil
}

// ParseFile parses a YAML file into the provided interface.
//
// Args:
// - filePath: Path to YAML file (must be pre-validated)
// - target: Pointer to struct to unmarshal into
//
// Returns: error if parsing fails, nil if successful
// Security: Assumes filePath already validated by ValidateFile
// Performance: Efficient unmarshaling using yaml.v3
// Side effects: Reads file from disk and modifies target struct
//
// Example:
//
//	var config MyConfig
//	err := yaml.ParseFile("config.yaml", &config)
func ParseFile(filePath string, target interface{}) error {
	// Read file content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return &ValidationError{
			Op:   "read_file",
			File: filePath,
			Err:  fmt.Errorf("cannot read file: %w", err),
		}
	}

	// Check for empty content
	if len(strings.TrimSpace(string(data))) == 0 {
		return &ValidationError{
			Op:   "validate_content",
			File: filePath,
			Err:  fmt.Errorf("file is empty or contains only whitespace"),
		}
	}

	// Parse YAML content
	if err := yaml.Unmarshal(data, target); err != nil {
		return &ValidationError{
			Op:   "parse_yaml",
			File: filePath,
			Err:  fmt.Errorf("invalid YAML format: %w", err),
		}
	}

	return nil
}

// ValidateStructure performs basic structure validation on parsed YAML.
//
// Args:
// - data: Parsed YAML data as interface{}
// - filePath: File path for error context
//
// Returns: error if structure is invalid, nil if valid
// Security: Prevents processing of malformed data structures
// Performance: Quick validation before expensive processing
func ValidateStructure(data interface{}, filePath string) error {
	if data == nil {
		return &ValidationError{
			Op:   "validate_structure",
			File: filePath,
			Err:  fmt.Errorf("parsed data is nil"),
		}
	}

	// Ensure data is a map (common requirement for configuration files)
	switch v := data.(type) {
	case map[string]interface{}:
		if len(v) == 0 {
			return &ValidationError{
				Op:   "validate_structure",
				File: filePath,
				Err:  fmt.Errorf("configuration is empty"),
			}
		}
	case map[interface{}]interface{}:
		if len(v) == 0 {
			return &ValidationError{
				Op:   "validate_structure",
				File: filePath,
				Err:  fmt.Errorf("configuration is empty"),
			}
		}
	default:
		return &ValidationError{
			Op:   "validate_structure",
			File: filePath,
			Err:  fmt.Errorf("expected configuration map, got %T", data),
		}
	}

	return nil
}
