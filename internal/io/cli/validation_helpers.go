package cli

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// File size limits for different file types
const (
	// MaxCSVFileSize limits CSV files to prevent memory exhaustion during processing
	// Args: Size in bytes (10MB default for large datasets)
	MaxCSVFileSize = 10 * 1024 * 1024

	// MaxYAMLFileSize limits YAML files to prevent memory exhaustion
	// Args: Size in bytes (1MB default for configuration files)
	MaxYAMLFileSize = 1024 * 1024

	// MaxModelFileSize limits model files to prevent memory exhaustion
	// Args: Size in bytes (50MB default for complex models)
	MaxModelFileSize = 50 * 1024 * 1024
)

// validateSecurePath performs security validation to prevent path traversal attacks.
//
// Args:
// - filePath: File path to validate for security issues
//
// Returns: error if path appears malicious, nil if secure
// Security: Prevents directory traversal and other path-based attacks
func validateSecurePath(filePath string) error {
	// Check for path traversal attempts
	if strings.Contains(filePath, "..") {
		return fmt.Errorf("path traversal detected: %s", filePath)
	}

	// Check for null bytes (can cause issues in some systems)
	if strings.Contains(filePath, "\x00") {
		return fmt.Errorf("null byte detected in path: %s", filePath)
	}

	// Check for suspicious patterns
	suspiciousPatterns := []string{
		"//",    // Double slashes
		"\\.\\", // Windows path traversal
	}

	for _, pattern := range suspiciousPatterns {
		if strings.Contains(filePath, pattern) {
			return fmt.Errorf("suspicious path pattern detected: %s", filePath)
		}
	}

	return nil
}

// validateFileComprehensive performs comprehensive file validation including security, existence, and properties.
//
// Args:
// - filePath: Path to the file to validate
// - fileType: Description of the file type for error messages (e.g., "input file", "model file")
// - maxSize: Maximum allowed file size in bytes (0 means no limit)
//
// Returns: error if any validation fails, nil if all validations pass
// Security: Includes path traversal protection and permission verification
// Performance: Early validation prevents expensive processing of invalid files
// Side effects: Checks file system for existence, permissions, and attempts file open
func validateFileComprehensive(filePath, fileType string, maxSize int64) error {
	// Basic path validation
	cleanPath := strings.TrimSpace(filePath)
	if cleanPath == "" {
		return fmt.Errorf("%s path is required", fileType)
	}

	// Security validation
	if err := validateSecurePath(cleanPath); err != nil {
		return fmt.Errorf("%s security validation failed: %w", fileType, err)
	}

	// Clean and normalize path
	cleanPath = filepath.Clean(cleanPath)

	// Check file existence and get info
	info, err := os.Stat(cleanPath)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", cleanPath)
	}
	if err != nil {
		return fmt.Errorf("cannot access file: %w", err)
	}

	// Verify it's a regular file
	if !info.Mode().IsRegular() {
		return fmt.Errorf("path is not a regular file: %s", cleanPath)
	}

	// Check file size limits if specified
	if maxSize > 0 && info.Size() > maxSize {
		return fmt.Errorf("file too large: %d bytes (max %d bytes)",
			info.Size(), maxSize)
	}

	// Verify read permissions by attempting to open
	file, err := os.Open(cleanPath)
	if err != nil {
		return fmt.Errorf("cannot read file: %w", err)
	}
	defer file.Close()

	return nil
}

// validateFileExistence validates that a file path is not empty, exists, and is not a directory.
//
// Args:
// - filePath: Path to the file to validate
// - fileType: Description of the file type for error messages (e.g., "input file", "model file")
//
// Returns error if path is empty, file doesn't exist, cannot be accessed, or is a directory.
// Note: This function is kept for backward compatibility. Use validateFileComprehensive for new code.
func validateFileExistence(filePath, fileType string) error {
	return validateFileComprehensive(filePath, fileType, 0)
}

// validateFileExtension validates that a file has one of the allowed extensions.
//
// Args:
// - filePath: Path to the file to validate
// - allowedExts: Slice of allowed extensions (e.g., []string{".csv", ".txt"})
// - fileType: Description of the file type for error messages
//
// Returns: error if extension is not in allowed list, nil if valid
func validateFileExtension(filePath string, allowedExts []string, fileType string) error {
	ext := strings.ToLower(filepath.Ext(filePath))

	for _, allowedExt := range allowedExts {
		if ext == strings.ToLower(allowedExt) {
			return nil
		}
	}

	if len(allowedExts) == 1 {
		return fmt.Errorf("%s must have %s extension, got: %s", fileType, allowedExts[0], ext)
	}

	return fmt.Errorf("%s must be in format %v, got: %s", fileType, allowedExts, ext)
}

// validateInputFile validates CSV input file existence and format.
//
// Args:
// - inputFile: Path to input CSV file
//
// Returns error if file doesn't exist, has wrong extension, or is directory.
// Security: Includes comprehensive validation with size limits and security checks
func validateInputFile(inputFile string) error {
	// Comprehensive validation with size limit
	if err := validateFileComprehensive(inputFile, "input file", MaxCSVFileSize); err != nil {
		return err
	}

	// Validate file extension
	return validateFileExtension(inputFile, []string{".csv"}, "input file")
}

// validateModelFile validates trained model file existence and format.
//
// Args:
// - modelFile: Path to trained model file
//
// Returns error if file doesn't exist or has wrong extension (.dt required).
// Security: Includes comprehensive validation with size limits and security checks
func validateModelFile(modelFile string) error {
	// Comprehensive validation with size limit
	if err := validateFileComprehensive(modelFile, "model file", MaxModelFileSize); err != nil {
		return err
	}

	// Validate file extension
	return validateFileExtension(modelFile, []string{".dt"}, "model file")
}

// validateYAMLFile validates YAML file existence and format.
//
// Args:
// - yamlFile: Path to YAML file
//
// Returns error if file doesn't exist or has wrong extension (.yaml/.yml required).
// Security: Includes comprehensive validation with size limits and security checks
func validateYAMLFile(yamlFile string) error {
	// Comprehensive validation with size limit
	if err := validateFileComprehensive(yamlFile, "YAML file", MaxYAMLFileSize); err != nil {
		return err
	}

	// Validate file extension
	return validateFileExtension(yamlFile, []string{".yaml", ".yml"}, "YAML file")
}
